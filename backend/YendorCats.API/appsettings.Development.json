{"ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=YendorCats;User=root;Password=password;Port=3306;"}, "DetailedErrors": true, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Vault": {"Address": "http://localhost:8200", "Token": "your-vault-token-will-be-set-here", "SecretPath": "secret/yendorcats/app-secrets"}, "AWS": {"Region": "us-west-004", "UseCredentialsFromSecrets": false, "S3": {"BucketName": "yendor", "UseDirectS3Urls": true, "ServiceUrl": "https://s3.us-west-004.backblazeb2.com", "PublicUrl": "https://f004.backblazeb2.com/file/yendor/{key}", "UseCdn": false, "CdnDomain": "", "AccessKey": "004d0cd685eb5360000000001", "SecretKey": "K004tG09sL3pGjAzB+d2BgD1z7g5A5I"}}}